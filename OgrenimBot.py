import os
import asyncio
import logging
from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from pyrogram.enums import ParseMode
import google.generativeai as genai
from google.generativeai.types import GenerateContentResponse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get credentials from environment variables for security
API_ID = int(os.getenv("API_ID", "29108471"))
API_HASH = os.getenv("API_HASH", "3c084bd30d228e5f40e98f298f3130a2")
BOT_TOKEN = os.getenv("BOT_TOKEN", "8041049657:AAHhprtlzkmxP9WH7xmAot3qVgLkJ2TfmLY")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "AIzaSyBq89pWqdkfhUpqKldFO_8-m3Xt_0M82ww")

# Configure Google Generative AI
genai.configure(api_key=GOOGLE_API_KEY)

# Initialize Pyrogram client
app = Client("OgrenimBot", api_id=API_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)

# Global dictionaries to store user data
temp_messages = {}
user_data = {}
user_last_request = {}  # For rate limiting

# Constants
MAX_TEXT_LENGTH = 10000
MIN_TEXT_WORDS = 5
MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB
ALLOWED_FILE_TYPES = ['.pdf']
RATE_LIMIT_SECONDS = 30  # Minimum time between requests

async def delete_temps(user_id):
    """Delete temporary messages for a user"""
    if user_id in temp_messages:
        for m in temp_messages[user_id]:
            try:
                await app.delete_messages(m.chat.id, m.id)
            except Exception as e:
                logger.warning(f"Failed to delete message: {e}")
        temp_messages[user_id] = []

async def send_temp(message):
    """Add message to temporary messages list"""
    if message.from_user.id not in temp_messages:
        temp_messages[message.from_user.id] = []
    temp_messages[message.from_user.id].append(message)
    return message

def check_rate_limit(user_id):
    """Check if user is rate limited"""
    import time
    current_time = time.time()

    if user_id in user_last_request:
        time_diff = current_time - user_last_request[user_id]
        if time_diff < RATE_LIMIT_SECONDS:
            return False, RATE_LIMIT_SECONDS - time_diff

    user_last_request[user_id] = current_time
    return True, 0

def validate_text_input(text):
    """Validate text input"""
    if not text or not text.strip():
        return False, "النص فارغ."

    if len(text) > MAX_TEXT_LENGTH:
        return False, f"النص طويل جداً. الحد الأقصى {MAX_TEXT_LENGTH} حرف."

    if len(text.split()) < MIN_TEXT_WORDS:
        return False, f"النص قصير جداً. يجب أن يحتوي على {MIN_TEXT_WORDS} كلمات على الأقل."

    return True, ""

def validate_file(document):
    """Validate uploaded file"""
    if not document.file_name:
        return False, "اسم الملف غير صالح."

    file_ext = os.path.splitext(document.file_name)[1].lower()
    if file_ext not in ALLOWED_FILE_TYPES:
        return False, f"نوع الملف غير مدعوم. الأنواع المدعومة: {', '.join(ALLOWED_FILE_TYPES)}"

    if document.file_size > MAX_FILE_SIZE:
        return False, f"حجم الملف كبير جداً. الحد الأقصى {MAX_FILE_SIZE // (1024*1024)} ميجابايت."

    return True, ""

@app.on_message(filters.command("start"))
async def start(client, message):
    """Handle /start command"""
    try:
        temp_messages[message.from_user.id] = []
        user_data[message.from_user.id] = {}
        m = await message.reply("أهلًا بك في Öğrenim!\nأرسل لي ملف PDF أو نصًا وسأولد لك أسئلة منه.")
        await send_temp(m)
    except Exception as e:
        logger.error(f"Error in start command: {e}")
        await message.reply("حدث خطأ، يرجى المحاولة مرة أخرى.")

@app.on_message(filters.document & filters.private)
async def handle_pdf(client, message):
    """Handle PDF document uploads"""
    try:
        # Check rate limit
        allowed, wait_time = check_rate_limit(message.from_user.id)
        if not allowed:
            await message.reply(f"يرجى الانتظار {int(wait_time)} ثانية قبل إرسال طلب جديد.")
            return

        # Validate file
        is_valid, error_msg = validate_file(message.document)
        if not is_valid:
            await message.reply(error_msg)
            return

        # Initialize user data if not exists
        if message.from_user.id not in user_data:
            user_data[message.from_user.id] = {}

        loading_msg = await message.reply("جاري تحميل الملف...")
        file_path = await message.download()
        user_data[message.from_user.id]["file_path"] = file_path
        await loading_msg.delete()
        await ask_question_count(message)
    except Exception as e:
        logger.error(f"Error handling PDF: {e}")
        await message.reply("حدث خطأ أثناء معالجة الملف، يرجى المحاولة مرة أخرى.")

@app.on_message(filters.text & filters.private & ~filters.command(["start"]))
async def handle_text(client, message):
    """Handle text messages"""
    try:
        # Check rate limit
        allowed, wait_time = check_rate_limit(message.from_user.id)
        if not allowed:
            await message.reply(f"يرجى الانتظار {int(wait_time)} ثانية قبل إرسال طلب جديد.")
            return

        # Validate text input
        is_valid, error_msg = validate_text_input(message.text)
        if not is_valid:
            m = await message.reply(error_msg)
            await send_temp(m)
            return

        # Initialize user data if not exists
        if message.from_user.id not in user_data:
            user_data[message.from_user.id] = {}

        user_data[message.from_user.id]["text"] = message.text
        await ask_question_count(message)
    except Exception as e:
        logger.error(f"Error handling text: {e}")
        await message.reply("حدث خطأ أثناء معالجة النص، يرجى المحاولة مرة أخرى.")

async def ask_question_count(message):
    """Ask user for number of questions"""
    try:
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("3", callback_data="count_3"),
             InlineKeyboardButton("5", callback_data="count_5"),
             InlineKeyboardButton("10", callback_data="count_10")]
        ])
        m = await message.reply("كم عدد الأسئلة التي تريد؟", reply_markup=keyboard)
        await send_temp(m)
    except Exception as e:
        logger.error(f"Error asking question count: {e}")
        await message.reply("حدث خطأ، يرجى المحاولة مرة أخرى.")

@app.on_callback_query(filters.regex(r"^count_\d+"))
async def set_question_count(client, query):
    """Handle question count selection"""
    try:
        await query.answer()
        count = int(query.data.split("_")[1])

        # Initialize user data if not exists
        if query.from_user.id not in user_data:
            user_data[query.from_user.id] = {}

        user_data[query.from_user.id]["count"] = count
        await ask_difficulty(query.message)
    except Exception as e:
        logger.error(f"Error setting question count: {e}")
        await query.answer("حدث خطأ، يرجى المحاولة مرة أخرى.")

async def ask_difficulty(message):
    """Ask user for difficulty level"""
    try:
        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton("سهل", callback_data="diff_easy"),
             InlineKeyboardButton("متوسط", callback_data="diff_medium"),
             InlineKeyboardButton("صعب", callback_data="diff_hard")]
        ])
        m = await message.reply("اختر مستوى الصعوبة:", reply_markup=keyboard)
        await send_temp(m)
    except Exception as e:
        logger.error(f"Error asking difficulty: {e}")
        await message.reply("حدث خطأ، يرجى المحاولة مرة أخرى.")

@app.on_callback_query(filters.regex(r"^diff_"))
async def set_difficulty(client, query):
    """Handle difficulty selection"""
    try:
        await query.answer()
        diff = query.data.split("_")[1]

        # Initialize user data if not exists
        if query.from_user.id not in user_data:
            user_data[query.from_user.id] = {}

        user_data[query.from_user.id]["difficulty"] = diff
        await query.message.reply("جاري توليد الأسئلة، انتظر لحظات...")
        await delete_temps(query.from_user.id)
        await generate_and_send(client, query.message, query.from_user.id)
    except Exception as e:
        logger.error(f"Error setting difficulty: {e}")
        await query.answer("حدث خطأ، يرجى المحاولة مرة أخرى.")

async def generate_and_send(client, message, user_id):
    """Generate questions using Gemini AI and send as polls"""
    try:
        data = user_data.get(user_id, {})
        if not data:
            await message.reply("لم يتم العثور على البيانات، يرجى البدء من جديد.")
            return

        # Create the prompt for question generation
        difficulty_map = {"easy": "سهل", "medium": "متوسط", "hard": "صعب"}
        difficulty_text = difficulty_map.get(data.get('difficulty', 'medium'), "متوسط")

        question_prompt = f"""
        اصنع {data.get('count', 5)} أسئلة اختيار من متعدد بمستوى صعوبة {difficulty_text}.

        تنسيق الإجابة المطلوب:
        السؤال الأول؟
        أ) الإجابة الصحيحة
        ب) إجابة خاطئة
        ج) إجابة خاطئة
        د) إجابة خاطئة

        السؤال الثاني؟
        أ) الإجابة الصحيحة
        ب) إجابة خاطئة
        ج) إجابة خاطئة
        د) إجابة خاطئة

        يجب أن تكون الإجابة الصحيحة دائماً في الخيار الأول (أ).
        """

        # Prepare content for Gemini
        if "file_path" in data:
            try:
                # Upload file to Gemini
                uploaded_file = genai.upload_file(path=data["file_path"])

                # Generate content with file
                model = genai.GenerativeModel('gemini-1.5-flash')
                response = model.generate_content([
                    uploaded_file,
                    question_prompt
                ])

                # Clean up uploaded file
                genai.delete_file(uploaded_file.name)

            except Exception as e:
                logger.error(f"Error processing file: {e}")
                await message.reply("حدث خطأ أثناء معالجة الملف، يرجى المحاولة مرة أخرى.")
                return
        else:
            try:
                # Generate content with text
                model = genai.GenerativeModel('gemini-1.5-flash')
                full_prompt = f"{question_prompt}\n\nالنص المرجعي:\n{data.get('text', '')}"
                response = model.generate_content(full_prompt)
            except Exception as e:
                logger.error(f"Error generating content: {e}")
                await message.reply("حدث خطأ أثناء توليد الأسئلة، يرجى المحاولة مرة أخرى.")
                return

        # Parse and send questions
        await parse_and_send_questions(client, message, response.text)

        # Clean up user data and files
        await cleanup_user_data(user_id)

        await message.reply("تم إرسال جميع الأسئلة 🎓\nهل ترغب باستخدام الخدمة مرة أخرى؟")

    except Exception as e:
        logger.error(f"Error in generate_and_send: {e}")
        await message.reply("حدث خطأ أثناء توليد الأسئلة، يرجى المحاولة مرة أخرى.")
        await cleanup_user_data(user_id)

async def parse_and_send_questions(client, message, response_text):
    """Parse AI response and send questions as polls"""
    try:
        # Split response into individual questions
        questions_blocks = response_text.split('\n\n')
        questions_sent = 0

        for block in questions_blocks:
            lines = [line.strip() for line in block.split('\n') if line.strip()]
            if len(lines) < 5:  # Need at least question + 4 options
                continue

            # Extract question (first line ending with ?)
            question_line = None
            for i, line in enumerate(lines):
                if line.endswith('؟') or line.endswith('?'):
                    question_line = line
                    options_start = i + 1
                    break

            if not question_line:
                continue

            # Extract options (lines starting with أ), ب), ج), د) or a), b), c), d))
            options = []
            for line in lines[options_start:]:
                if any(line.startswith(prefix) for prefix in ['أ)', 'ب)', 'ج)', 'د)', 'a)', 'b)', 'c)', 'd)']):
                    # Remove the prefix and clean the option
                    option_text = line[2:].strip()
                    if option_text:
                        options.append(option_text)

            # Ensure we have exactly 4 options
            if len(options) >= 4:
                options = options[:4]  # Take first 4 options

                try:
                    # Send poll (correct answer is always the first option - index 0)
                    await client.send_poll(
                        chat_id=message.chat.id,
                        question=question_line,
                        options=options,
                        correct_option_id=0,
                        type="quiz"
                    )
                    questions_sent += 1

                    # Add small delay between polls to avoid rate limiting
                    await asyncio.sleep(0.5)

                except Exception as e:
                    logger.error(f"Error sending poll: {e}")
                    continue

        if questions_sent == 0:
            await message.reply("لم أتمكن من إنشاء أسئلة صالحة، يرجى المحاولة مرة أخرى.")

    except Exception as e:
        logger.error(f"Error parsing questions: {e}")
        await message.reply("حدث خطأ أثناء معالجة الأسئلة.")

async def cleanup_user_data(user_id):
    """Clean up user data and temporary files"""
    try:
        # Remove uploaded file if exists
        if user_id in user_data and "file_path" in user_data[user_id]:
            file_path = user_data[user_id]["file_path"]
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Removed file: {file_path}")

        # Clear user data
        temp_messages.pop(user_id, None)
        user_data.pop(user_id, None)

    except Exception as e:
        logger.error(f"Error cleaning up user data: {e}")

async def periodic_cleanup():
    """Periodic cleanup of old user data"""
    import time
    current_time = time.time()

    # Clean up old rate limit data (older than 1 hour)
    users_to_remove = []
    for user_id, last_request in user_last_request.items():
        if current_time - last_request > 3600:  # 1 hour
            users_to_remove.append(user_id)

    for user_id in users_to_remove:
        user_last_request.pop(user_id, None)

    if users_to_remove:
        logger.info(f"Cleaned up rate limit data for {len(users_to_remove)} users")

# Error handler for unhandled exceptions
@app.on_message()
async def handle_unhandled_messages(client, message):
    """Handle any unhandled messages"""
    try:
        # Only respond to private messages that aren't handled by other handlers
        if message.chat.type == "private" and not message.text.startswith('/'):
            # Check if user has ongoing session
            if message.from_user.id not in user_data:
                await message.reply("يرجى البدء بإرسال /start أولاً.")
    except Exception as e:
        logger.error(f"Error in unhandled message handler: {e}")

if __name__ == "__main__":
    logger.info("Starting OgrenimBot...")

    # Schedule periodic cleanup
    import asyncio
    async def run_with_cleanup():
        # Start periodic cleanup task
        cleanup_task = asyncio.create_task(periodic_cleanup_loop())

        # Start the bot
        try:
            await app.start()
            logger.info("Bot started successfully!")
            await asyncio.Event().wait()  # Keep running
        except KeyboardInterrupt:
            logger.info("Bot stopped by user")
        finally:
            cleanup_task.cancel()
            await app.stop()

    async def periodic_cleanup_loop():
        """Run periodic cleanup every hour"""
        while True:
            try:
                await asyncio.sleep(3600)  # 1 hour
                await periodic_cleanup()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")

    # Run the bot
    try:
        asyncio.run(run_with_cleanup())
    except KeyboardInterrupt:
        logger.info("Bot stopped")
    except Exception as e:
        logger.error(f"Fatal error: {e}")