import os
from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from pyrogram.enums import ParseMode
from google import genai
import google.generativeai as genai
from google.generativeai import Client as GeminiClient
from google.generativeai import GenerateContentConfig
from google.generativeai import Content, Part

Api_id = 29108471
api_hash = "3c084bd30d228e5f40e98f298f3130a2"
BOT_TOKEN = "8041049657:AAHhprtlzkmxP9WH7xmAot3qVgLkJ2TfmLY"
GOOGLE_API_KEY = 'AIzaSyBq89pWqdkfhUpqKldFO_8-m3Xt_0M82ww'

genai_client = GeminiClient(api_key=GOOGLE_API_KEY)
app = Client("OgrenimBot", api_id=Api_id, api_hash=api_hash, bot_token=BOT_TOKEN)

temp_messages = {}
user_data = {}

def delete_temps(user_id):
    if user_id in temp_messages:
        for m in temp_messages[user_id]:
            try:
                app.delete_messages(m.chat.id, m.id)
            except:
                pass
        temp_messages[user_id] = []

async def send_temp(message):
    if message.from_user.id not in temp_messages:
        temp_messages[message.from_user.id] = []
    temp_messages[message.from_user.id].append(message)
    return message

@app.on_message(filters.command("start"))
async def start(client, message):
    temp_messages[message.from_user.id] = []
    user_data[message.from_user.id] = {}
    m = await message.reply("أهلًا بك في Öğrenim!\nأرسل لي ملف PDF أو نصًا وسأولد لك أسئلة منه.")
    await send_temp(m)

@app.on_message(filters.document & filters.private)
async def handle_pdf(client, message):
    file_path = await message.download()
    user_data[message.from_user.id]["file_path"] = file_path
    await ask_question_count(message)

@app.on_message(filters.text & filters.private)
async def handle_text(client, message):
    if len(message.text.split()) < 5:
        m = await message.reply("النص قصير جدًا لتوليد أسئلة.")
        await send_temp(m)
        return
    user_data[message.from_user.id]["text"] = message.text
    await ask_question_count(message)

async def ask_question_count(message):
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("3", callback_data="count_3"),
         InlineKeyboardButton("5", callback_data="count_5"),
         InlineKeyboardButton("10", callback_data="count_10")]
    ])
    m = await message.reply("كم عدد الأسئلة التي تريد؟", reply_markup=keyboard)
    await send_temp(m)

@app.on_callback_query(filters.regex(r"^count_\d+"))
async def set_question_count(client, query):
    count = int(query.data.split("_")[1])
    user_data[query.from_user.id]["count"] = count
    await ask_difficulty(query.message)

async def ask_difficulty(message):
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("سهل", callback_data="diff_easy"),
         InlineKeyboardButton("متوسط", callback_data="diff_medium"),
         InlineKeyboardButton("صعب", callback_data="diff_hard")]
    ])
    m = await message.reply("اختر مستوى الصعوبة:", reply_markup=keyboard)
    await send_temp(m)

@app.on_callback_query(filters.regex(r"^diff_"))
async def set_difficulty(client, query):
    diff = query.data.split("_")[1]
    user_data[query.from_user.id]["difficulty"] = diff
    await query.message.reply("جاري توليد الأسئلة، انتظر لحظات...")
    await delete_temps(query.from_user.id)
    await generate_and_send(client, query.message, query.from_user.id)

async def generate_and_send(client, message, user_id):
    data = user_data[user_id]
    question_prompt = f"اصنع {data['count']} أسئلة اختيار من متعدد بصعوبة {data['difficulty']}، بصيغة Poll مع خيار واحد صحيح والباقي خاطئ."

    if "file_path" in data:
        file = genai_client.files.upload(file=data["file_path"])
        contents = [
            Content(role="user", parts=[Part.from_uri(file.uri, file.mime_type)]),
            Content(role="user", parts=[Part.from_text("السلام عليكم")])
        ]
    else:
        contents = [
            Content(role="user", parts=[Part.from_text(data["text"])]),
        ]

    config = GenerateContentConfig(
        temperature=0.7,
        top_p=1,
        top_k=1,
        max_output_tokens=2048,
        response_mime_type="text/plain",
        system_instruction=[Part.from_text(question_prompt)]
        )

    model = "gemini-2.5-flash-preview-05-20"
    result = ""
    for chunk in genai_client.models.generate_content_stream(model=model, contents=contents, config=config):
        result += chunk.text

    questions = [q.strip() for q in result.split("\n\n") if q.strip()]
    for q in questions:
        parts = q.split("\n")
        question_text = parts[0]
        options = parts[1:]
        if len(options) < 2:
            continue
        correct = options[0]
        incorrect = options[1:4]
        all_options = [correct] + incorrect
        await client.send_poll(message.chat.id, question=question_text, options=all_options, correct_option_id=0)

    await message.reply("تم إرسال جميع الأسئلة 🎓\nهل ترغب باستخدام الخدمة مرة أخرى؟")
    temp_messages.pop(user_id, None)
    user_data.pop(user_id, None)

app.run()