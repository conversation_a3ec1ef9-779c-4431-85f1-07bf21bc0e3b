import os
from pyrogram import Client, filters
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton
import google.generativeai as genai

API_ID = 29108471
API_HASH = "3c084bd30d228e5f40e98f298f3130a2"
BOT_TOKEN = "8041049657:AAHhprtlzkmxP9WH7xmAot3qVgLkJ2TfmLY"
GOOGLE_API_KEY = "AIzaSyBq89pWqdkfhUpqKldFO_8-m3Xt_0M82ww"

genai.configure(api_key=GOOGLE_API_KEY)
app = Client("OgrenimBot", api_id=API_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)

user_data = {}
temp_messages = {}

async def delete_temp_messages(user_id):
    if user_id in temp_messages:
        for msg in temp_messages[user_id]:
            try:
                await app.delete_messages(msg.chat.id, msg.id)
            except:
                pass
        temp_messages[user_id] = []

@app.on_message(filters.command("start"))
async def start(client, message):
    user_data[message.from_user.id] = {}
    temp_messages[message.from_user.id] = []
    await message.reply("أهلاً بك في Öğrenim!\nأرسل لي ملف PDF أو نصاً وسأولد لك أسئلة منه.")

@app.on_message(filters.document & filters.private)
async def handle_pdf(client, message):
    if not message.document.file_name.lower().endswith('.pdf'):
        await message.reply("يرجى إرسال ملف PDF فقط.")
        return

    if message.document.file_size > 20 * 1024 * 1024:
        await message.reply("حجم الملف كبير جداً.")
        return

    user_data[message.from_user.id] = {}
    file_path = await message.download()
    user_data[message.from_user.id]["file_path"] = file_path
    await ask_question_count(message)

@app.on_message(filters.text & filters.private & ~filters.command(["start"]))
async def handle_text(client, message):
    if len(message.text.split()) < 5:
        await message.reply("النص قصير جداً لتوليد أسئلة.")
        return

    user_data[message.from_user.id] = {"text": message.text}
    await ask_question_count(message)

async def ask_question_count(message):
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("3", callback_data="count_3"),
         InlineKeyboardButton("5", callback_data="count_5"),
         InlineKeyboardButton("10", callback_data="count_10")]
    ])
    msg = await message.reply("كم عدد الأسئلة التي تريد؟", reply_markup=keyboard)
    if message.from_user.id not in temp_messages:
        temp_messages[message.from_user.id] = []
    temp_messages[message.from_user.id].append(msg)

@app.on_callback_query(filters.regex(r"^count_\d+"))
async def set_question_count(client, query):
    await query.answer()
    count = int(query.data.split("_")[1])
    user_data[query.from_user.id]["count"] = count

    try:
        await query.message.delete()
    except:
        pass

    await ask_difficulty(query.message)

async def ask_difficulty(message):
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("سهل", callback_data="diff_easy"),
         InlineKeyboardButton("متوسط", callback_data="diff_medium"),
         InlineKeyboardButton("صعب", callback_data="diff_hard")]
    ])
    msg = await message.reply("اختر مستوى الصعوبة:", reply_markup=keyboard)
    if message.from_user.id not in temp_messages:
        temp_messages[message.from_user.id] = []
    temp_messages[message.from_user.id].append(msg)

@app.on_callback_query(filters.regex(r"^diff_"))
async def set_difficulty(client, query):
    await query.answer()
    diff = query.data.split("_")[1]
    user_data[query.from_user.id]["difficulty"] = diff

    try:
        await query.message.delete()
    except:
        pass

    await delete_temp_messages(query.from_user.id)
    loading_msg = await query.message.reply("جاري توليد الأسئلة...")
    await generate_and_send(client, query.message, query.from_user.id, loading_msg)

async def generate_and_send(client, message, user_id, loading_msg):
    try:
        data = user_data[user_id]

        if "file_path" in data:
            prompt = f"""Create {data['count']} multiple choice questions with {data['difficulty']} difficulty based on the file content. Use the same language as the file.

Format exactly like this:

Question 1?
a) Correct answer
b) Wrong answer
c) Wrong answer
d) Wrong answer

Question 2?
a) Correct answer
b) Wrong answer
c) Wrong answer
d) Wrong answer

IMPORTANT: Put correct answer as option 'a)' always."""
        else:
            prompt = f"""اصنع {data['count']} أسئلة اختيار من متعدد بصعوبة {data['difficulty']} من النص التالي.

التنسيق المطلوب:

السؤال الأول؟
أ) الإجابة الصحيحة
ب) إجابة خاطئة
ج) إجابة خاطئة
د) إجابة خاطئة

السؤال الثاني؟
أ) الإجابة الصحيحة
ب) إجابة خاطئة
ج) إجابة خاطئة
د) إجابة خاطئة

مهم: الإجابة الصحيحة دائماً في الخيار 'أ)'."""

        model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')

        if "file_path" in data:
            uploaded_file = None
            try:
                print(f"📁 Uploading file: {data['file_path']}")
                uploaded_file = genai.upload_file(path=data["file_path"])
                print(f"✅ File uploaded successfully: {uploaded_file.name}")

                response = model.generate_content(
                    [uploaded_file, prompt],
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.7,
                        max_output_tokens=2000
                    )
                )
                print("✅ Content generated successfully")

            except Exception as file_error:
                print(f"❌ File processing error: {file_error}")
                raise Exception(f"خطأ في معالجة الملف: {str(file_error)}")
            finally:
                # تنظيف الملفات في جميع الحالات
                try:
                    if uploaded_file:
                        genai.delete_file(uploaded_file.name)
                        print("🗑️ Deleted file from Gemini")
                except Exception as e:
                    print(f"⚠️ Warning: Could not delete from Gemini: {e}")

                try:
                    if os.path.exists(data["file_path"]):
                        os.remove(data["file_path"])
                        print(f"🗑️ Deleted local file: {data['file_path']}")
                except Exception as e:
                    print(f"⚠️ Warning: Could not delete local file: {e}")
        else:
            full_prompt = f"{prompt}\n\nالنص:\n{data['text']}"
            response = model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=2000
                )
            )

        # التحقق من وجود استجابة
        if not response or not response.text:
            raise Exception("لم يتم الحصول على استجابة من AI")

        print(f"Response received, length: {len(response.text)}")

        # إرسال الأسئلة
        questions_sent = await parse_and_send_questions(client, message, response.text)

        # حذف رسالة التحميل
        try:
            await loading_msg.delete()
        except:
            pass

        # إرسال رسالة النتيجة
        if questions_sent > 0:
            await message.reply(f"تم إرسال {questions_sent} أسئلة 🎓")
        else:
            await message.reply("لم أتمكن من إنشاء أسئلة، يرجى المحاولة مرة أخرى.")

        user_data.pop(user_id, None)
    except Exception as e:
        # تنظيف الملفات في حالة الخطأ العام
        if user_id in user_data and "file_path" in user_data[user_id]:
            try:
                file_path = user_data[user_id]["file_path"]
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"🗑️ Emergency cleanup: Deleted {file_path}")
            except Exception as cleanup_error:
                print(f"⚠️ Emergency cleanup failed: {cleanup_error}")

        try:
            await loading_msg.delete()
        except:
            pass
        await message.reply(f"حدث خطأ: {str(e)}")
        user_data.pop(user_id, None)

async def parse_and_send_questions(client, message, response_text):
    questions_sent = 0

    print("=== RESPONSE ANALYSIS ===")
    print(f"Response length: {len(response_text)}")
    print(f"First 800 chars: {response_text[:800]}")
    print("=" * 50)

    # تقسيم النص بطرق مختلفة للعثور على الأسئلة
    blocks = response_text.split('\n\n')
    if len(blocks) < 2:
        blocks = response_text.split('\n')

    questions_data = []

    # البحث في كل block
    for block in blocks:
        lines = [line.strip() for line in block.split('\n') if line.strip()]

        if len(lines) < 5:  # نحتاج سؤال + 4 خيارات على الأقل
            continue

        # البحث عن السؤال (أول سطر ينتهي بعلامة استفهام)
        question = None
        options = []

        for line in lines:
            if line.endswith('؟') or line.endswith('?'):
                question = line
                break

        if not question:
            # إذا لم نجد علامة استفهام، نأخذ أول سطر
            question = lines[0]

        # البحث عن الخيارات
        for line in lines[1:]:  # تجاهل السطر الأول (السؤال)
            if any(line.startswith(prefix) for prefix in
                ['أ)', 'ب)', 'ج)', 'د)', 'a)', 'b)', 'c)', 'd)', 'A)', 'B)', 'C)', 'D)',
                 '1)', '2)', '3)', '4)', '1.', '2.', '3.', '4.', 'a.', 'b.', 'c.', 'd.']):

                # استخراج النص
                if line.startswith(('1.', '2.', '3.', '4.', 'a.', 'b.', 'c.', 'd.')):
                    option_text = line[2:].strip()
                else:
                    option_text = line[2:].strip()

                if option_text and len(options) < 4:
                    options.append(option_text)

        # إضافة السؤال إذا كان لدينا 4 خيارات
        if question and len(options) >= 4:
            questions_data.append((question, options[:4]))
            print(f"📝 Found question: {question[:50]}...")
            print(f"   Options: {len(options)}")

    print(f"🔍 Total questions found: {len(questions_data)}")

    # إرسال الأسئلة
    for i, (question, options) in enumerate(questions_data, 1):
        try:
            await client.send_poll(
                chat_id=message.chat.id,
                question=question,
                options=options,
                type="quiz mode",
                correct_option_id=0,
                is_anonymous=False,
                allows_multiple_answers=False,
                close_date=None
            )
            questions_sent += 1
            print(f"✅ Sent quiz {i}: {question[:30]}...")
        except Exception as e:
            print(f"❌ Error sending quiz {i}: {e}")

    return questions_sent

app.run()