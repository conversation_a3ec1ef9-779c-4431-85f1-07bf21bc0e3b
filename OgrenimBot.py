import os
from pyrogram import Client, filters
from pyrogram.types import InlineKeyboardMarkup, InlineKeyboardButton
import google.generativeai as genai

API_ID = 29108471
API_HASH = "3c084bd30d228e5f40e98f298f3130a2"
BOT_TOKEN = "8041049657:AAHhprtlzkmxP9WH7xmAot3qVgLkJ2TfmLY"
GOOGLE_API_KEY = "AIzaSyBq89pWqdkfhUpqKldFO_8-m3Xt_0M82ww"

genai.configure(api_key=GOOGLE_API_KEY)
app = Client("OgrenimBot", api_id=API_ID, api_hash=API_HASH, bot_token=BOT_TOKEN)

user_data = {}

@app.on_message(filters.command("start"))
async def start(client, message):
    user_data[message.from_user.id] = {}
    await message.reply("أهلاً بك في Öğrenim!\nأرسل لي ملف PDF أو نصاً وسأولد لك أسئلة منه.")

@app.on_message(filters.document & filters.private)
async def handle_pdf(client, message):
    if not message.document.file_name.lower().endswith('.pdf'):
        await message.reply("يرجى إرسال ملف PDF فقط.")
        return

    if message.document.file_size > 20 * 1024 * 1024:
        await message.reply("حجم الملف كبير جداً.")
        return

    user_data[message.from_user.id] = {}
    file_path = await message.download()
    user_data[message.from_user.id]["file_path"] = file_path
    await ask_question_count(message)

@app.on_message(filters.text & filters.private & ~filters.command(["start"]))
async def handle_text(client, message):
    if len(message.text.split()) < 5:
        await message.reply("النص قصير جداً لتوليد أسئلة.")
        return

    user_data[message.from_user.id] = {"text": message.text}
    await ask_question_count(message)

async def ask_question_count(message):
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("3", callback_data="count_3"),
         InlineKeyboardButton("5", callback_data="count_5"),
         InlineKeyboardButton("10", callback_data="count_10")]
    ])
    await message.reply("كم عدد الأسئلة التي تريد؟", reply_markup=keyboard)

@app.on_callback_query(filters.regex(r"^count_\d+"))
async def set_question_count(client, query):
    await query.answer()
    count = int(query.data.split("_")[1])
    user_data[query.from_user.id]["count"] = count
    await ask_difficulty(query.message)

async def ask_difficulty(message):
    keyboard = InlineKeyboardMarkup([
        [InlineKeyboardButton("سهل", callback_data="diff_easy"),
         InlineKeyboardButton("متوسط", callback_data="diff_medium"),
         InlineKeyboardButton("صعب", callback_data="diff_hard")]
    ])
    await message.reply("اختر مستوى الصعوبة:", reply_markup=keyboard)

@app.on_callback_query(filters.regex(r"^diff_"))
async def set_difficulty(client, query):
    await query.answer()
    diff = query.data.split("_")[1]
    user_data[query.from_user.id]["difficulty"] = diff
    await query.message.reply("جاري توليد الأسئلة...")
    await generate_and_send(client, query.message, query.from_user.id)

async def generate_and_send(client, message, user_id):
    data = user_data[user_id]

    prompt = f"اصنع {data['count']} أسئلة اختيار من متعدد بصعوبة {data['difficulty']}. كل سؤال يجب أن يحتوي على 4 خيارات والإجابة الصحيحة في الخيار الأول."

    if "file_path" in data:
        uploaded_file = genai.upload_file(path=data["file_path"])
        model = genai.GenerativeModel('gemini-1.5-flash')
        response = model.generate_content([uploaded_file, prompt])
        genai.delete_file(uploaded_file.name)
        os.remove(data["file_path"])
    else:
        model = genai.GenerativeModel('gemini-1.5-flash')
        full_prompt = f"{prompt}\n\nالنص:\n{data['text']}"
        response = model.generate_content(full_prompt)

    await parse_and_send_questions(client, message, response.text)
    await message.reply("تم إرسال الأسئلة 🎓")
    user_data.pop(user_id, None)

async def parse_and_send_questions(client, message, response_text):
    questions_blocks = response_text.split('\n\n')

    for block in questions_blocks:
        lines = [line.strip() for line in block.split('\n') if line.strip()]
        if len(lines) < 5:
            continue

        question_line = None
        for i, line in enumerate(lines):
            if line.endswith('؟') or line.endswith('?'):
                question_line = line
                options_start = i + 1
                break

        if not question_line:
            continue

        options = []
        for line in lines[options_start:]:
            if any(line.startswith(prefix) for prefix in ['أ)', 'ب)', 'ج)', 'د)', 'a)', 'b)', 'c)', 'd)']):
                option_text = line[2:].strip()
                if option_text:
                    options.append(option_text)

        if len(options) >= 4:
            options = options[:4]
            try:
                await client.send_poll(
                    chat_id=message.chat.id,
                    question=question_line,
                    options=options,
                    correct_option_id=0,
                    type="quiz"
                )
            except:
                continue

app.run()