from pyrogram import Client, filters
from pyrogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
import requests

api_id = 29108471
api_hash = "3c084bd30d228e5f40e98f298f3130a2"
BOT_TOKEN = "7301398385:AAG7bUSIM0EpW8xEw3nked55Sf3s19xZjT4"  
OPENROUTER_API_KEY = "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" 

app = Client("kodlar_bot", api_id=api_id, api_hash=api_hash, bot_token=BOT_TOKEN)

user_code = {}

buttons = [
    [InlineKeyboardButton("🔁 تحويل كود", callback_data="translate_code")],
    [InlineKeyboardButton("🛠 تصحيح أخطاء", callback_data="fix_code")]
]

languages = [
    ["Python", "JavaScript"],
    ["C++", "Java"],
    ["C#", "Go"],
]

def get_language_buttons():
    return InlineKeyboardMarkup(
        [[InlineKeyboardButton(lang, callback_data=f"lang_{lang}")] for row in languages for lang in row]
    )

@app.on_message(filters.command("start"))
async def start(client, message: Message):
    await message.reply(
          f"مرحباً بِكَ يا  {message.from_user.username}⚡️ \n لطفاً اخبرني بما تُريد 🖤",
        reply_markup=InlineKeyboardMarkup(buttons)
    )

@app.on_callback_query(filters.regex("translate_code"))
async def translate_code(client, callback_query: CallbackQuery):
    user_id = callback_query.from_user.id
    user_code[user_id] = {"step": "awaiting_code", "mode": "translate"}
    await callback_query.message.edit_text("لُطفاً ارفق لي الكود المراد تحويله ✨")

@app.on_callback_query(filters.regex("fix_code"))
async def fix_code(client, callback_query: CallbackQuery):
    user_id = callback_query.from_user.id
    user_code[user_id] = {"step": "awaiting_code", "mode": "fix"}
    await callback_query.message.edit_text("✍️ أرسل الكود الذي تريد تصحيحه:")

@app.on_callback_query(filters.regex("lang_"))
async def handle_language_choice(client, callback_query: CallbackQuery):
    user_id = callback_query.from_user.id
    if user_id not in user_code or user_code[user_id].get("mode") != "translate":
        await callback_query.answer("لا توجد عملية تحويل جارية.")
        return

    lang = callback_query.data.split("_")[1]
    user_code[user_id]["language"] = lang
    await callback_query.message.delete()
    await process_code_translation(callback_query.message, user_id)

@app.on_message(filters.text & ~filters.command(["start"]))
async def receive_code(client, message: Message):
    user_id = message.from_user.id

    if user_id not in user_code:
        await message.reply("❗️يرجى اختيار وظيفة أولاً باستخدام /start.")
        return

    if user_code[user_id]["step"] == "awaiting_code":
        user_code[user_id]["code"] = message.text
        if user_code[user_id]["mode"] == "translate":
            user_code[user_id]["step"] = "awaiting_language"
            await message.reply("🌐 اختر اللغة التي تريد التحويل إليها:", reply_markup=get_language_buttons())
        elif user_code[user_id]["mode"] == "fix":
            await process_code_fix(message, user_id)

async def process_code_translation(message: Message, user_id):
    code = user_code[user_id].get("code")
    target_lang = user_code[user_id].get("language")

    loading_msg = await message.reply("🔄 جاري تحويل الكود...")

    prompt = f"رجاءً حوّل الكود التالي إلى لغة {target_lang} فقط بدون شرح أو تعليق:\n\n{code}"

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "model": "mistralai/mixtral-8x7b-instruct",
        "messages": [
            {"role": "system", "content": "أنت مساعد مبرمج محترف لتحويل الكود بين لغات البرمجة المختلفة."},
            {"role": "user", "content": prompt}
        ]
    }

    try:
        response = requests.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=payload)
        res_data = response.json()
        if "choices" in res_data and len(res_data["choices"]) > 0:
            result = res_data["choices"][0]["message"]["content"]
        else:
            result = "❌ لم أتمكن من تحويل الكود."
    except Exception as e:
        result = f"❌ حدث خطأ أثناء تحويل الكود:\n{str(e)}"

    await loading_msg.delete()
    await message.reply(f"📤 الكود بعد التحويل:\n\n{result}")
    await message.reply("إِن كانَ لكَ طَلبُ آخر 🤍", reply_markup=InlineKeyboardMarkup(buttons))
    user_code.pop(user_id, None)


async def process_code_fix(message: Message, user_id):
    code = user_code[user_id].get("code")

    loading_msg = await message.reply("🛠 جاري تصحيح الكود...")

    prompt = f"الرجاء مراجعة وتصحيح الكود التالي، وشرح الخطأ إن أمكن:\n\n{code}"

    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "model": "mistralai/mixtral-8x7b-instruct",
        "messages": [
            {"role": "system", "content": "أنت مساعد مبرمج متخصص في اكتشاف وتصحيح الأخطاء البرمجية مع الشرح."},
            {"role": "user", "content": prompt}
        ]
    }

    try:
        response = requests.post("https://openrouter.ai/api/v1/chat/completions", headers=headers, json=payload)
        res_data = response.json()
        if "choices" in res_data and len(res_data["choices"]) > 0:
            result = res_data["choices"][0]["message"]["content"]
        else:
            result = "❌ لم أتمكن من تحليل الكود أو إيجاد الأخطاء."
    except Exception as e:
        result = f"❌ حدث خطأ أثناء مراجعة الكود:\n{str(e)}"

    await loading_msg.delete()
    await message.reply(f"🔍 النتيجة:\n\n{result}")
    await message.reply("إِن كانَ لكَ طَلبُ آخر 🤍", reply_markup=InlineKeyboardMarkup(buttons))
    user_code.pop(user_id, None)

app.run()

